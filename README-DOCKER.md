# MCP Feedback Enhanced - Docker 部署指南

本文档提供了使用Docker部署MCP Feedback Enhanced的完整指南，包括开发环境和生产环境的配置。

## 🐳 Docker 部署概述

### 支持的部署模式

1. **开发/测试环境** - 使用 `docker-compose.yml`
2. **生产环境** - 使用 `docker-compose.prod.yml`（包含Nginx反向代理和Redis缓存）

### 镜像特性

- **多阶段构建** - 优化镜像大小
- **非root用户** - 增强安全性
- **健康检查** - 自动监控服务状态
- **资源限制** - 防止资源滥用
- **日志管理** - 结构化日志输出

## 🚀 快速开始

### 方法一：使用快速启动脚本

```bash
# 克隆项目
git clone https://github.com/Minidoracat/mcp-feedback-enhanced.git
cd mcp-feedback-enhanced

# 运行快速启动脚本
chmod +x quick-start.sh
./quick-start.sh
```

### 方法二：使用Makefile命令

```bash
# 构建并启动服务
make docker-quick-start

# 或者分步执行
make docker-build
make docker-up
```

### 方法三：手动执行Docker Compose

```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

## 📋 环境配置

### 环境变量设置

1. **复制环境变量模板**
```bash
cp .env.example .env
```

2. **编辑环境变量**
```bash
vim .env
```

### 重要环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MCP_WEB_HOST` | `0.0.0.0` | 服务绑定地址 |
| `MCP_WEB_PORT` | `8765` | 服务端口 |
| `MCP_DEBUG` | `false` | 调试模式 |
| `MCP_MAX_SESSIONS` | `10` | 最大并发会话数 |
| `REDIS_PASSWORD` | `mcpredis123` | Redis密码 |

## 🏗️ 架构说明

### 开发环境架构

```
┌─────────────────┐
│   MCP App       │
│   Port: 8765    │
└─────────────────┘
```

### 生产环境架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │────│    MCP App      │────│     Redis       │
│  Port: 80/443   │    │   Port: 8765    │    │   Port: 6379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 管理命令

### 基本操作

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 完全清理
docker-compose down -v
docker system prune -f
```

### 使用Makefile

```bash
# 查看所有可用命令
make help

# Docker相关命令
make docker-build         # 构建镜像
make docker-up            # 启动服务
make docker-down          # 停止服务
make docker-logs          # 查看日志
make docker-prod          # 启动生产环境
make docker-clean         # 清理资源
```

## 🔒 生产环境配置

### SSL证书配置

1. **自签名证书（测试用）**
```bash
mkdir -p ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout ssl/key.pem \
    -out ssl/cert.pem \
    -subj "/C=CN/ST=State/L=City/O=Organization/CN=your-domain.com"
```

2. **Let's Encrypt证书（推荐）**
```bash
# 安装certbot
sudo yum install -y certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 更新nginx配置中的证书路径
ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
```

### 防火墙配置

```bash
# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload

# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw reload
```

## 📊 监控和维护

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:8765/health

# 检查所有服务状态
docker-compose ps
```

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f mcp-feedback-enhanced

# 查看错误日志
docker-compose logs | grep ERROR
```

### 性能监控

```bash
# 查看资源使用情况
docker stats

# 查看容器详细信息
docker inspect mcp-feedback-enhanced
```

## 🔄 更新和备份

### 应用更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose build --no-cache
docker-compose up -d
```

### 数据备份

```bash
# 备份数据目录
tar -czf mcp-backup-$(date +%Y%m%d).tar.gz data/

# 备份配置文件
tar -czf mcp-config-backup-$(date +%Y%m%d).tar.gz .env docker-compose*.yml
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :8765
sudo ss -tlnp | grep :8765

# 修改端口（在.env文件中）
MCP_WEB_PORT=8766
```

2. **权限问题**
```bash
# 检查目录权限
ls -la data/ logs/

# 修复权限
sudo chown -R 1000:1000 data/ logs/
```

3. **内存不足**
```bash
# 检查内存使用
free -h
docker stats

# 调整资源限制（在docker-compose.yml中）
deploy:
  resources:
    limits:
      memory: 256M
```

4. **SSL证书问题**
```bash
# 检查证书有效性
openssl x509 -in ssl/cert.pem -text -noout

# 重新生成证书
rm ssl/*.pem
make generate-ssl-cert
```

### 调试模式

```bash
# 启用调试模式
echo "MCP_DEBUG=true" >> .env

# 重启服务
docker-compose restart

# 查看详细日志
docker-compose logs -f
```

## 📞 技术支持

### 获取帮助

1. 查看项目文档：[GitHub Repository](https://github.com/Minidoracat/mcp-feedback-enhanced)
2. 提交Issue：[GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
3. 查看部署文档：`DEPLOYMENT.md`

### 提交Bug报告

请提供以下信息：
- 操作系统版本
- Docker版本
- 错误日志
- 复现步骤
- 环境变量配置（隐藏敏感信息）

```bash
# 收集系统信息
echo "OS: $(uname -a)"
echo "Docker: $(docker --version)"
echo "Docker Compose: $(docker-compose --version)"
echo "Services: $(docker-compose ps)"
```
