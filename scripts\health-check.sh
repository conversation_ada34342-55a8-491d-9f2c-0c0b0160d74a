#!/bin/bash

# MCP Feedback Enhanced - 健康检查脚本
# 用于Docker容器的健康检查

set -e

# 配置
HEALTH_URL="http://localhost:8765/health"
TIMEOUT=10
MAX_RETRIES=3

# 健康检查函数
check_health() {
    local retry_count=0
    
    while [ $retry_count -lt $MAX_RETRIES ]; do
        if curl -f -s --max-time $TIMEOUT "$HEALTH_URL" > /dev/null 2>&1; then
            echo "Health check passed"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        echo "Health check attempt $retry_count failed, retrying..."
        sleep 2
    done
    
    echo "Health check failed after $MAX_RETRIES attempts"
    return 1
}

# 执行健康检查
check_health
