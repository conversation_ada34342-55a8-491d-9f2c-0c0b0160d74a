[bumpversion]
current_version = 2.6.0
commit = False
tag = False
allow_dirty = True
parse = (?P<major>\d+)\.(?P<minor>\d+)\.(?P<patch>\d+)
serialize = {major}.{minor}.{patch}

[bumpversion:file:pyproject.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:src/mcp_feedback_enhanced/__init__.py]
search = __version__ = "{current_version}"
replace = __version__ = "{new_version}"
