# MCP Feedback Enhanced - 环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# ===== 基本服务配置 =====
# Web服务主机地址（容器内使用 0.0.0.0，本地开发可用 127.0.0.1）
MCP_WEB_HOST=0.0.0.0

# Web服务端口
MCP_WEB_PORT=8765

# 调试模式（生产环境设为 false）
MCP_DEBUG=false

# 测试模式（生产环境设为 false）
MCP_TEST_MODE=false

# ===== 路径配置 =====
# 数据存储目录
MCP_DATA_DIR=/app/data

# 日志存储目录
MCP_LOG_DIR=/app/logs

# 临时文件目录
MCP_TEMP_DIR=/app/temp

# ===== 性能配置 =====
# 最大并发会话数
MCP_MAX_SESSIONS=10

# 会话超时时间（秒）
MCP_SESSION_TIMEOUT=3600

# 最大文件上传大小（字节）
MCP_MAX_FILE_SIZE=10485760

# ===== 安全配置 =====
# Redis密码（如果使用Redis缓存）
REDIS_PASSWORD=mcpredis123

# JWT密钥（如果需要认证）
# JWT_SECRET_KEY=your-secret-key-here

# ===== SSL配置 =====
# SSL证书路径（生产环境）
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ===== 数据库配置（如果需要）=====
# DATABASE_URL=postgresql://user:password@localhost:5432/mcp_feedback
# DATABASE_POOL_SIZE=10
# DATABASE_MAX_OVERFLOW=20

# ===== 监控配置 =====
# 启用性能监控
# ENABLE_MONITORING=true

# 监控数据保留天数
# MONITORING_RETENTION_DAYS=30

# ===== 日志配置 =====
# 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
LOG_LEVEL=INFO

# 日志格式（json, text）
LOG_FORMAT=json

# 日志文件最大大小（MB）
LOG_MAX_SIZE=50

# 日志文件保留数量
LOG_BACKUP_COUNT=5

# ===== 国际化配置 =====
# 默认语言
DEFAULT_LANGUAGE=zh-CN

# 支持的语言列表
SUPPORTED_LANGUAGES=zh-CN,en-US,zh-TW

# ===== 开发环境配置 =====
# 开发模式（启用热重载等）
# DEVELOPMENT_MODE=false

# 自动重载
# AUTO_RELOAD=false

# ===== Docker配置 =====
# 容器用户ID
APP_UID=1000

# 容器组ID
APP_GID=1000

# 容器用户名
APP_USER=mcpuser

# ===== 网络配置 =====
# 允许的主机列表（逗号分隔）
# ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# CORS允许的源（逗号分隔）
# CORS_ORIGINS=http://localhost:3000,https://your-frontend.com

# ===== 备份配置 =====
# 自动备份间隔（小时）
# BACKUP_INTERVAL=24

# 备份保留天数
# BACKUP_RETENTION_DAYS=7

# 备份存储路径
# BACKUP_PATH=/app/backups
