#!/usr/bin/env python3
"""
简单的Web服务器测试，绕过复杂的端口管理
"""

import os
import sys
import tempfile
import time
import threading
from pathlib import Path

# 设置环境变量禁用复杂功能
os.environ["MCP_DEBUG"] = "true"
os.environ["MCP_TEST_MODE"] = "true"
os.environ["MCP_WEB_HOST"] = "127.0.0.1"
os.environ["MCP_WEB_PORT"] = "8765"

print("🚀 简单Web服务器测试")
print("="*40)

try:
    print("📦 导入FastAPI...")
    from fastapi import FastAPI
    from fastapi.responses import HTMLResponse
    import uvicorn
    print("✅ FastAPI导入成功")
    
    print("🔧 创建简单的FastAPI应用...")
    app = FastAPI(title="MCP Test Server")
    
    @app.get("/", response_class=HTMLResponse)
    async def root():
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>MCP Test Server</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .success { color: green; }
                .info { color: blue; }
            </style>
        </head>
        <body>
            <h1 class="success">✅ MCP Feedback Enhanced 测试服务器</h1>
            <p class="info">🎉 恭喜！Web服务器成功启动！</p>
            <p>这是一个简化的测试服务器，用于验证基本的Web功能。</p>
            <h2>测试信息</h2>
            <ul>
                <li>服务器地址: http://127.0.0.1:8765</li>
                <li>Python版本: """ + sys.version + """</li>
                <li>工作目录: """ + str(Path.cwd()) + """</li>
            </ul>
            <h2>下一步</h2>
            <p>如果您能看到这个页面，说明基本的Web服务功能正常。</p>
            <p>现在可以尝试启动完整的MCP Feedback Enhanced服务。</p>
        </body>
        </html>
        """
    
    @app.get("/health")
    async def health():
        return {"status": "ok", "message": "Test server is running"}
    
    print("✅ FastAPI应用创建成功")
    
    def run_server():
        """在单独线程中运行服务器"""
        try:
            print("🚀 启动Uvicorn服务器...")
            uvicorn.run(
                app,
                host="127.0.0.1",
                port=8765,
                log_level="info",
                access_log=True
            )
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
    
    print("🔧 在后台线程启动服务器...")
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 测试连接
    print("🔍 测试服务器连接...")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 8765))
        sock.close()
        
        if result == 0:
            print("✅ 服务器连接测试成功！")
            print("🌐 请在浏览器中访问: http://127.0.0.1:8765")
            print("💡 按 Ctrl+C 停止服务器")
            
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 停止服务器...")
        else:
            print(f"❌ 服务器连接测试失败 (错误码: {result})")
            
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
