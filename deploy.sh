#!/bin/bash

# MCP Feedback Enhanced - CentOS 部署脚本
# 自动化部署脚本，包含环境检查、依赖安装、服务部署等功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="mcp-feedback-enhanced"
DEPLOY_DIR="/opt/${PROJECT_NAME}"
SERVICE_USER="mcpuser"
DOCKER_COMPOSE_VERSION="2.20.0"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_info "检查系统版本..."
    
    if [[ -f /etc/redhat-release ]]; then
        local version=$(cat /etc/redhat-release)
        log_info "检测到系统: $version"
        
        if [[ $version == *"CentOS"* ]] || [[ $version == *"Red Hat"* ]] || [[ $version == *"Rocky"* ]] || [[ $version == *"AlmaLinux"* ]]; then
            log_success "支持的系统版本"
        else
            log_warning "未测试的系统版本，可能存在兼容性问题"
        fi
    else
        log_error "不支持的系统，此脚本仅支持CentOS/RHEL/Rocky/AlmaLinux"
        exit 1
    fi
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新系统
    yum update -y
    
    # 安装基础工具
    yum install -y \
        curl \
        wget \
        git \
        unzip \
        vim \
        htop \
        net-tools \
        firewalld \
        yum-utils \
        device-mapper-persistent-data \
        lvm2
    
    log_success "系统依赖安装完成"
}

# 安装Docker
install_docker() {
    log_info "检查Docker安装状态..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker已安装，版本: $(docker --version)"
    else
        log_info "安装Docker..."
        
        # 添加Docker官方仓库
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        
        # 安装Docker
        yum install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin
        
        # 启动Docker服务
        systemctl start docker
        systemctl enable docker
        
        log_success "Docker安装完成"
    fi
    
    # 检查Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装，版本: $(docker-compose --version)"
    else
        log_info "安装Docker Compose..."
        
        # 下载Docker Compose
        curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        
        # 设置执行权限
        chmod +x /usr/local/bin/docker-compose
        
        # 创建软链接
        ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
        
        log_success "Docker Compose安装完成"
    fi
}

# 创建服务用户
create_service_user() {
    log_info "创建服务用户..."
    
    if id "$SERVICE_USER" &>/dev/null; then
        log_info "用户 $SERVICE_USER 已存在"
    else
        useradd -r -s /bin/false -d "$DEPLOY_DIR" "$SERVICE_USER"
        log_success "用户 $SERVICE_USER 创建完成"
    fi
    
    # 将用户添加到docker组
    usermod -aG docker "$SERVICE_USER" || true
}

# 创建部署目录
create_deploy_directory() {
    log_info "创建部署目录..."
    
    mkdir -p "$DEPLOY_DIR"/{data,logs,temp,ssl,nginx/logs,backups}
    
    # 设置目录权限
    chown -R "$SERVICE_USER:$SERVICE_USER" "$DEPLOY_DIR"
    chmod -R 755 "$DEPLOY_DIR"
    
    log_success "部署目录创建完成: $DEPLOY_DIR"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 启动防火墙服务
    systemctl start firewalld
    systemctl enable firewalld
    
    # 开放必要端口
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --permanent --add-port=443/tcp
    firewall-cmd --permanent --add-port=8765/tcp
    
    # 重载防火墙配置
    firewall-cmd --reload
    
    log_success "防火墙配置完成"
}

# 生成SSL证书（自签名）
generate_ssl_cert() {
    log_info "生成SSL证书..."
    
    local ssl_dir="$DEPLOY_DIR/ssl"
    
    if [[ ! -f "$ssl_dir/cert.pem" ]]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$ssl_dir/key.pem" \
            -out "$ssl_dir/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
        
        chown "$SERVICE_USER:$SERVICE_USER" "$ssl_dir"/*.pem
        chmod 600 "$ssl_dir"/*.pem
        
        log_success "SSL证书生成完成"
        log_warning "使用的是自签名证书，生产环境请使用正式证书"
    else
        log_info "SSL证书已存在"
    fi
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    # 复制项目文件到部署目录
    cp -r . "$DEPLOY_DIR/app/"
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_USER" "$DEPLOY_DIR/app"
    
    # 切换到部署目录
    cd "$DEPLOY_DIR/app"
    
    # 创建环境变量文件
    if [[ ! -f .env ]]; then
        cp .env.example .env
        log_info "已创建环境变量文件，请根据需要修改 $DEPLOY_DIR/app/.env"
    fi
    
    # 构建和启动服务
    sudo -u "$SERVICE_USER" docker-compose -f docker-compose.prod.yml build
    sudo -u "$SERVICE_USER" docker-compose -f docker-compose.prod.yml up -d
    
    log_success "应用部署完成"
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    cat > /etc/systemd/system/${PROJECT_NAME}.service << EOF
[Unit]
Description=MCP Feedback Enhanced Service
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=${DEPLOY_DIR}/app
ExecStart=/usr/local/bin/docker-compose -f docker-compose.prod.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.prod.yml down
User=${SERVICE_USER}
Group=${SERVICE_USER}

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable ${PROJECT_NAME}.service
    
    log_success "systemd服务创建完成"
}

# 主函数
main() {
    log_info "开始部署 MCP Feedback Enhanced..."
    
    check_root
    check_system
    install_dependencies
    install_docker
    create_service_user
    create_deploy_directory
    configure_firewall
    generate_ssl_cert
    deploy_application
    create_systemd_service
    
    log_success "部署完成！"
    log_info "服务访问地址:"
    log_info "  HTTP:  http://$(hostname -I | awk '{print $1}'):80"
    log_info "  HTTPS: https://$(hostname -I | awk '{print $1}'):443"
    log_info "  直接访问: http://$(hostname -I | awk '{print $1}'):8765"
    log_info ""
    log_info "管理命令:"
    log_info "  启动服务: systemctl start ${PROJECT_NAME}"
    log_info "  停止服务: systemctl stop ${PROJECT_NAME}"
    log_info "  查看状态: systemctl status ${PROJECT_NAME}"
    log_info "  查看日志: docker-compose -f ${DEPLOY_DIR}/app/docker-compose.prod.yml logs -f"
}

# 执行主函数
main "$@"
