# MCP Feedback Enhanced - Docker Compose 配置
# 生产环境部署配置，包含完整的服务定义和最佳实践

version: '3.8'

services:
  mcp-feedback-enhanced:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - APP_USER=mcpuser
        - APP_UID=1000
        - APP_GID=1000
    
    image: mcp-feedback-enhanced:latest
    container_name: mcp-feedback-enhanced
    
    # 端口映射
    ports:
      - "8765:8765"
    
    # 环境变量配置
    environment:
      # 服务配置
      - MCP_WEB_HOST=0.0.0.0
      - MCP_WEB_PORT=8765
      - MCP_DEBUG=false
      - MCP_TEST_MODE=false
      
      # 路径配置
      - MCP_DATA_DIR=/app/data
      - MCP_LOG_DIR=/app/logs
      - MCP_TEMP_DIR=/app/temp
      
      # 性能配置
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      
      # 可选：自定义配置
      # - MCP_MAX_SESSIONS=10
      # - MCP_SESSION_TIMEOUT=3600
      # - MCP_MAX_FILE_SIZE=10485760
    
    # 卷挂载 - 持久化数据
    volumes:
      - mcp_data:/app/data
      - mcp_logs:/app/logs
      - mcp_temp:/app/temp
      # 可选：挂载配置文件
      # - ./config:/app/config:ro
    
    # 网络配置
    networks:
      - mcp_network
    
    # 重启策略
    restart: unless-stopped
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # 安全配置
    security_opt:
      - no-new-privileges:true
    
    # 只读根文件系统（除了必要的写入目录）
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

# 网络定义
networks:
  mcp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 卷定义
volumes:
  mcp_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  
  mcp_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
  
  mcp_temp:
    driver: local
