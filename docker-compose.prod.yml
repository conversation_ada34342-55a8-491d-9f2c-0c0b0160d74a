# MCP Feedback Enhanced - 生产环境 Docker Compose 配置
# 包含反向代理、SSL终端、监控等生产环境必需组件

version: '3.8'

services:
  # 主应用服务
  mcp-feedback-enhanced:
    build:
      context: .
      dockerfile: Dockerfile
    image: mcp-feedback-enhanced:latest
    container_name: mcp-feedback-enhanced-app
    
    environment:
      - MCP_WEB_HOST=0.0.0.0
      - MCP_WEB_PORT=8765
      - MCP_DEBUG=false
      - MCP_DATA_DIR=/app/data
      - MCP_LOG_DIR=/app/logs
      - MCP_TEMP_DIR=/app/temp
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    volumes:
      - mcp_data:/app/data
      - mcp_logs:/app/logs
      - mcp_temp:/app/temp
    
    networks:
      - mcp_internal
    
    restart: unless-stopped
    
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
    
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=200m

  # Nginx 反向代理
  nginx:
    image: nginx:1.25-alpine
    container_name: mcp-feedback-enhanced-nginx
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    
    networks:
      - mcp_internal
      - mcp_external
    
    depends_on:
      - mcp-feedback-enhanced
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis 缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: mcp-feedback-enhanced-redis
    
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-mcpredis123}
    
    volumes:
      - redis_data:/data
    
    networks:
      - mcp_internal
    
    restart: unless-stopped
    
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# 网络定义
networks:
  mcp_internal:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/16
  
  mcp_external:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 卷定义
volumes:
  mcp_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mcp-feedback-enhanced/data
  
  mcp_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mcp-feedback-enhanced/logs
  
  mcp_temp:
    driver: local
  
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mcp-feedback-enhanced/nginx/logs
  
  redis_data:
    driver: local
