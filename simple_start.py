#!/usr/bin/env python3
"""
简单启动脚本 - 直接启动 Web UI
"""

import os
import tempfile
import time

# 设置环境变量
os.environ["MCP_DEBUG"] = "true"
os.environ["MCP_WEB_HOST"] = "127.0.0.1"
os.environ["MCP_WEB_PORT"] = "8765"

print("🚀 启动 MCP Feedback Enhanced Web UI")
print("="*40)

try:
    print("📦 导入模块...")
    from mcp_feedback_enhanced.web.main import WebUIManager
    print("✅ 模块导入成功")
    
    print("🔧 创建管理器...")
    manager = WebUIManager()
    print(f"✅ 管理器创建成功，地址: {manager.host}:{manager.port}")
    
    print("📝 创建测试会话...")
    with tempfile.TemporaryDirectory() as temp_dir:
        content = "# 本地测试\n\n这是一个本地运行测试。"
        session_id = manager.create_session(temp_dir, content)
        print(f"✅ 会话创建成功: {session_id}")
        
        print("🚀 启动服务器...")
        manager.start_server()
        
        print("⏳ 等待服务器启动...")
        time.sleep(5)
        
        if manager.server_thread and manager.server_thread.is_alive():
            url = f"http://{manager.host}:{manager.port}"
            print(f"✅ 服务器启动成功！")
            print(f"🌐 访问地址: {url}")
            print("💡 请在浏览器中打开上述地址")
            print("💡 按 Ctrl+C 停止服务器")
            
            # 保持运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 停止服务器...")
        else:
            print("❌ 服务器启动失败")
            
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
