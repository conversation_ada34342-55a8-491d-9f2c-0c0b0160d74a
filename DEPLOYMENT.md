# MCP Feedback Enhanced - CentOS 部署指南

本文档提供了在CentOS服务器上部署MCP Feedback Enhanced的完整指南。

## 📋 系统要求

### 最低配置
- **操作系统**: CentOS 7/8/9, RHEL 7/8/9, Rocky Linux 8/9, AlmaLinux 8/9
- **CPU**: 1核心
- **内存**: 1GB RAM
- **存储**: 10GB 可用空间
- **网络**: 可访问互联网（用于下载依赖）

### 推荐配置
- **CPU**: 2核心或更多
- **内存**: 2GB RAM或更多
- **存储**: 20GB 可用空间或更多
- **网络**: 稳定的互联网连接

## 🚀 快速部署

### 方法一：自动部署脚本（推荐）

1. **下载项目代码**
```bash
# 克隆项目
git clone https://github.com/Minidoracat/mcp-feedback-enhanced.git
cd mcp-feedback-enhanced

# 或者下载并解压
wget https://github.com/Minidoracat/mcp-feedback-enhanced/archive/main.zip
unzip main.zip
cd mcp-feedback-enhanced-main
```

2. **运行自动部署脚本**
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本（需要root权限）
sudo ./deploy.sh
```

3. **访问服务**
- HTTP: `http://your-server-ip:80`
- HTTPS: `https://your-server-ip:443`
- 直接访问: `http://your-server-ip:8765`

### 方法二：手动部署

#### 步骤1：安装Docker和Docker Compose

```bash
# 更新系统
sudo yum update -y

# 安装必要工具
sudo yum install -y yum-utils device-mapper-persistent-data lvm2

# 添加Docker仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose
```

#### 步骤2：准备部署环境

```bash
# 创建部署目录
sudo mkdir -p /opt/mcp-feedback-enhanced/{data,logs,ssl,nginx/logs}

# 创建服务用户
sudo useradd -r -s /bin/false mcpuser
sudo usermod -aG docker mcpuser

# 设置目录权限
sudo chown -R mcpuser:mcpuser /opt/mcp-feedback-enhanced
```

#### 步骤3：配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（根据需要修改）
vim .env
```

#### 步骤4：生成SSL证书

```bash
# 生成自签名证书（生产环境请使用正式证书）
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /opt/mcp-feedback-enhanced/ssl/key.pem \
    -out /opt/mcp-feedback-enhanced/ssl/cert.pem \
    -subj "/C=CN/ST=State/L=City/O=Organization/CN=your-domain.com"

sudo chown mcpuser:mcpuser /opt/mcp-feedback-enhanced/ssl/*.pem
sudo chmod 600 /opt/mcp-feedback-enhanced/ssl/*.pem
```

#### 步骤5：部署应用

```bash
# 构建镜像
docker-compose -f docker-compose.prod.yml build

# 启动服务
docker-compose -f docker-compose.prod.yml up -d
```

#### 步骤6：配置防火墙

```bash
# 启动防火墙
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 开放端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8765/tcp
sudo firewall-cmd --reload
```

## 🔧 配置选项

### 环境变量配置

主要环境变量说明：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MCP_WEB_HOST` | `0.0.0.0` | Web服务绑定地址 |
| `MCP_WEB_PORT` | `8765` | Web服务端口 |
| `MCP_DEBUG` | `false` | 调试模式 |
| `MCP_MAX_SESSIONS` | `10` | 最大并发会话数 |
| `MCP_SESSION_TIMEOUT` | `3600` | 会话超时时间（秒） |
| `MCP_MAX_FILE_SIZE` | `10485760` | 最大文件上传大小（字节） |

### 资源限制配置

在 `docker-compose.prod.yml` 中可以调整资源限制：

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'      # CPU限制
      memory: 1G       # 内存限制
    reservations:
      cpus: '0.5'      # CPU预留
      memory: 256M     # 内存预留
```

## 📊 监控和维护

### 服务管理命令

```bash
# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 停止服务
docker-compose -f docker-compose.prod.yml down

# 更新服务
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:8765/health

# 检查Nginx状态
curl http://localhost:80/health
```

### 日志管理

日志文件位置：
- 应用日志: `/opt/mcp-feedback-enhanced/logs/`
- Nginx日志: `/opt/mcp-feedback-enhanced/nginx/logs/`
- Docker日志: `docker-compose logs`

### 备份策略

```bash
# 备份数据目录
tar -czf mcp-backup-$(date +%Y%m%d).tar.gz /opt/mcp-feedback-enhanced/data/

# 备份配置文件
tar -czf mcp-config-backup-$(date +%Y%m%d).tar.gz \
    /opt/mcp-feedback-enhanced/app/.env \
    /opt/mcp-feedback-enhanced/app/docker-compose.prod.yml
```

## 🔒 安全配置

### SSL证书配置

生产环境建议使用Let's Encrypt或购买的SSL证书：

```bash
# 使用Let's Encrypt（需要域名）
sudo yum install -y certbot
sudo certbot certonly --standalone -d your-domain.com

# 更新nginx配置中的证书路径
ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
```

### 防火墙配置

```bash
# 只允许特定IP访问（可选）
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='***********/24' port protocol='tcp' port='8765' accept"

# 限制SSH访问（推荐）
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='your-admin-ip' service name='ssh' accept"
sudo firewall-cmd --permanent --remove-service=ssh
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :8765
# 或
sudo ss -tlnp | grep :8765
```

2. **权限问题**
```bash
# 检查文件权限
ls -la /opt/mcp-feedback-enhanced/
# 修复权限
sudo chown -R mcpuser:mcpuser /opt/mcp-feedback-enhanced/
```

3. **Docker服务问题**
```bash
# 检查Docker状态
sudo systemctl status docker
# 重启Docker
sudo systemctl restart docker
```

4. **内存不足**
```bash
# 检查内存使用
free -h
# 检查Docker容器资源使用
docker stats
```

### 日志分析

```bash
# 查看应用错误日志
docker-compose -f docker-compose.prod.yml logs mcp-feedback-enhanced | grep ERROR

# 查看Nginx错误日志
docker-compose -f docker-compose.prod.yml logs nginx | grep error
```

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 提交新的Issue并提供详细的错误信息和环境描述

项目地址：https://github.com/Minidoracat/mcp-feedback-enhanced
