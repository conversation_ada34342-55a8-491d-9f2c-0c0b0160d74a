#!/bin/bash

# MCP Feedback Enhanced - 快速启动脚本
# 用于开发和测试环境的快速部署

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 MCP Feedback Enhanced 快速启动${NC}"
echo "=================================="
echo ""
echo "💡 使用提示："
echo "   标准启动: ./quick-start.sh"
echo "   强制重建: ./quick-start.sh --fr"
echo "   完全重建: ./quick-start.sh --fr --ci"
echo "   查看帮助: ./quick-start.sh --help"
echo ""

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${YELLOW}⚠️  Docker未安装，请先安装Docker${NC}"
    exit 1
fi

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${YELLOW}⚠️  Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 定义Docker Compose命令（兼容新旧版本）
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
else
    DOCKER_COMPOSE="docker compose"
fi

# 添加命令行参数支持
FORCE_REBUILD=false
CLEAN_IMAGES=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --fr)
            FORCE_REBUILD=true
            shift
            ;;
        --ci)
            CLEAN_IMAGES=true
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --fr    强制重新构建（清理所有容器、卷和网络）"
            echo "  --ci     同时清理镜像（强制重新构建镜像）"
            echo "  --help, -h         显示此帮助信息"
            exit 0
            ;;
        *)
            echo -e "${YELLOW}⚠️  未知参数: $1${NC}"
            echo "使用 --help 查看可用选项"
            exit 1
            ;;
    esac
done

# 使用 Docker Compose 标准命令进行环境清理
echo -e "${BLUE}🔍 检查现有 Docker Compose 环境...${NC}"

# 检查是否有运行中的服务
if $DOCKER_COMPOSE ps --services --filter "status=running" 2>/dev/null | grep -q .; then
    echo -e "${YELLOW}⚠️  发现运行中的服务，正在停止...${NC}"
    SERVICES_FOUND=true
else
    # 检查是否有已停止的服务
    if $DOCKER_COMPOSE ps --services 2>/dev/null | grep -q .; then
        echo -e "${YELLOW}⚠️  发现已停止的服务...${NC}"
        SERVICES_FOUND=true
    else
        SERVICES_FOUND=false
    fi
fi

# 执行清理操作
if [ "$SERVICES_FOUND" = true ] || [ "$FORCE_REBUILD" = true ]; then
    echo -e "${BLUE}🧹 使用 Docker Compose 清理环境...${NC}"

    if [ "$CLEAN_IMAGES" = true ]; then
        echo -e "${YELLOW}🗑️  执行完全清理（包括镜像）...${NC}"
        $DOCKER_COMPOSE down --rmi all --volumes --remove-orphans 2>/dev/null || true
        echo -e "${GREEN}✅ 完全清理完成（容器、网络、卷、镜像）${NC}"
    else
        echo -e "${YELLOW}🗑️  执行标准清理...${NC}"
        $DOCKER_COMPOSE down --volumes --remove-orphans 2>/dev/null || true
        echo -e "${GREEN}✅ 标准清理完成（容器、网络、卷）${NC}"
    fi
else
    echo -e "${GREEN}✅ 环境干净，无需清理${NC}"
fi

# 创建必要的目录
echo -e "${BLUE}📁 创建数据目录...${NC}"
mkdir -p data logs temp

# 复制环境变量文件
if [ ! -f .env ]; then
    echo -e "${BLUE}📝 创建环境变量文件...${NC}"
    cp .env.example .env 2>/dev/null || echo -e "${YELLOW}⚠️  未找到 .env.example 文件${NC}"
    if [ -f .env ]; then
        echo -e "${YELLOW}💡 已创建 .env 文件，可根据需要修改配置${NC}"
    fi
fi

# 构建镜像
echo -e "${BLUE}🔨 构建Docker镜像...${NC}"
if [ "$CLEAN_IMAGES" = true ] || [ "$FORCE_REBUILD" = true ]; then
    echo -e "${BLUE}🔄 强制重新构建镜像（无缓存）...${NC}"
    $DOCKER_COMPOSE build --no-cache
else
    echo -e "${BLUE}🔄 构建镜像（使用缓存）...${NC}"
    $DOCKER_COMPOSE build
fi

# 启动服务
echo -e "${BLUE}🚀 启动服务...${NC}"
$DOCKER_COMPOSE up -d

# 等待服务启动
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
echo -e "${BLUE}🔍 检查服务状态...${NC}"
if $DOCKER_COMPOSE ps | grep -q "Up"; then
    echo -e "${GREEN}✅ 服务启动成功！${NC}"
    echo ""
    echo "🌐 访问地址："
    echo "   http://localhost:8765"
    echo ""
    echo "📊 管理命令："
    echo "   查看状态: $DOCKER_COMPOSE ps"
    echo "   查看日志: $DOCKER_COMPOSE logs -f"
    echo "   停止服务: $DOCKER_COMPOSE down"
    echo "   重启服务: $DOCKER_COMPOSE restart"
    echo "   标准清理: $DOCKER_COMPOSE down --volumes --remove-orphans"
    echo "   完全清理: $DOCKER_COMPOSE down --rmi all --volumes --remove-orphans"
    echo ""
    echo "📝 配置文件："
    echo "   环境变量: .env"
    echo "   Docker配置: docker-compose.yml"
    echo ""
    echo "🔧 故障排除："
    echo "   标准重启: ./quick-start.sh"
    echo "   强制重建: ./quick-start.sh --fr"
    echo "   完全重建: ./quick-start.sh --fr --ci"
    echo "   查看帮助: ./quick-start.sh --help"
else
    echo -e "${RED}❌ 服务启动失败！${NC}"
    echo ""
    echo "🔍 故障排除步骤："
    echo "1. 查看容器状态: $DOCKER_COMPOSE ps"
    echo "2. 查看详细日志: $DOCKER_COMPOSE logs"
    echo "3. 检查端口占用: netstat -tlnp | grep 8765"
    echo "4. 手动清理重试: $DOCKER_COMPOSE down && ./quick-start.sh"
    echo ""
    echo "📋 当前容器状态："
    $DOCKER_COMPOSE ps
fi
