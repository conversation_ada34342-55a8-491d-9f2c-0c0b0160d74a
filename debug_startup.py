#!/usr/bin/env python3
"""
详细调试启动脚本
"""

import os
import sys
import tempfile
import time
import traceback
from pathlib import Path

def debug_print(msg):
    """调试输出"""
    print(f"[DEBUG] {msg}")
    sys.stdout.flush()

def test_imports():
    """测试所有必要的导入"""
    debug_print("开始测试导入...")
    
    try:
        debug_print("导入 mcp_feedback_enhanced...")
        import mcp_feedback_enhanced
        debug_print(f"✅ mcp_feedback_enhanced 版本: {mcp_feedback_enhanced.__version__}")
        
        debug_print("导入 WebUIManager...")
        from mcp_feedback_enhanced.web.main import WebUIManager
        debug_print("✅ WebUIManager 导入成功")
        
        debug_print("导入其他依赖...")
        import fastapi
        import uvicorn
        import jinja2
        debug_print("✅ 所有依赖导入成功")
        
        return True
    except Exception as e:
        debug_print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_web_ui_creation():
    """测试 WebUIManager 创建"""
    debug_print("测试 WebUIManager 创建...")
    
    try:
        # 设置环境变量
        os.environ["MCP_DEBUG"] = "true"
        os.environ["MCP_WEB_HOST"] = "127.0.0.1"
        os.environ["MCP_WEB_PORT"] = "8765"
        
        debug_print("创建 WebUIManager 实例...")
        from mcp_feedback_enhanced.web.main import WebUIManager
        
        manager = WebUIManager()
        debug_print(f"✅ WebUIManager 创建成功")
        debug_print(f"   主机: {manager.host}")
        debug_print(f"   端口: {manager.port}")
        
        return manager
    except Exception as e:
        debug_print(f"❌ WebUIManager 创建失败: {e}")
        traceback.print_exc()
        return None

def test_session_creation(manager):
    """测试会话创建"""
    debug_print("测试会话创建...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            debug_print(f"临时目录: {temp_dir}")
            
            content = """# 调试测试

## 测试内容
这是一个调试测试会话。

### 功能测试
- Web UI 启动
- 会话管理
- 内容渲染
"""
            
            debug_print("创建会话...")
            session_id = manager.create_session(temp_dir, content)
            
            if session_id:
                debug_print(f"✅ 会话创建成功: {session_id}")
                return session_id, temp_dir
            else:
                debug_print("❌ 会话创建失败")
                return None, None
                
    except Exception as e:
        debug_print(f"❌ 会话创建异常: {e}")
        traceback.print_exc()
        return None, None

def test_server_startup(manager):
    """测试服务器启动"""
    debug_print("测试服务器启动...")
    
    try:
        debug_print("调用 start_server()...")
        manager.start_server()
        
        debug_print("等待服务器启动...")
        time.sleep(3)
        
        debug_print("检查服务器状态...")
        if hasattr(manager, 'server_thread') and manager.server_thread:
            debug_print(f"服务器线程存在: {manager.server_thread.is_alive()}")
            if manager.server_thread.is_alive():
                debug_print("✅ 服务器线程运行中")
                return True
            else:
                debug_print("❌ 服务器线程已停止")
                return False
        else:
            debug_print("❌ 服务器线程不存在")
            return False
            
    except Exception as e:
        debug_print(f"❌ 服务器启动异常: {e}")
        traceback.print_exc()
        return False

def test_port_access():
    """测试端口访问"""
    debug_print("测试端口访问...")
    
    try:
        import socket
        
        debug_print("创建socket连接测试...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        result = sock.connect_ex(('127.0.0.1', 8765))
        sock.close()
        
        if result == 0:
            debug_print("✅ 端口 8765 可访问")
            return True
        else:
            debug_print(f"❌ 端口 8765 不可访问 (错误码: {result})")
            return False
            
    except Exception as e:
        debug_print(f"❌ 端口测试异常: {e}")
        return False

def main():
    """主函数"""
    debug_print("🚀 开始详细调试...")
    debug_print("="*50)
    
    debug_print(f"Python 版本: {sys.version}")
    debug_print(f"工作目录: {Path.cwd()}")
    debug_print(f"Python 路径: {sys.path[:3]}...")  # 只显示前3个路径
    
    # 1. 测试导入
    debug_print("\n1️⃣ 测试导入...")
    if not test_imports():
        debug_print("❌ 导入测试失败，退出")
        return False
    
    # 2. 测试 WebUIManager 创建
    debug_print("\n2️⃣ 测试 WebUIManager 创建...")
    manager = test_web_ui_creation()
    if not manager:
        debug_print("❌ WebUIManager 创建失败，退出")
        return False
    
    # 3. 测试会话创建
    debug_print("\n3️⃣ 测试会话创建...")
    session_id, temp_dir = test_session_creation(manager)
    if not session_id:
        debug_print("❌ 会话创建失败，退出")
        return False
    
    # 4. 测试服务器启动
    debug_print("\n4️⃣ 测试服务器启动...")
    if not test_server_startup(manager):
        debug_print("❌ 服务器启动失败")
        return False
    
    # 5. 测试端口访问
    debug_print("\n5️⃣ 测试端口访问...")
    if test_port_access():
        debug_print("✅ 所有测试通过！")
        url = f"http://{manager.host}:{manager.port}"
        debug_print(f"🌐 服务器运行在: {url}")
        debug_print("💡 请在浏览器中访问上述地址")
        debug_print("💡 按 Ctrl+C 停止服务器")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            debug_print("\n🛑 停止服务器...")
            return True
    else:
        debug_print("❌ 端口访问测试失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        debug_print(f"❌ 调试过程中发生错误: {e}")
        traceback.print_exc()
        sys.exit(1)
