# MCP Feedback Enhanced - Production Dockerfile
# 基于Python 3.11的多阶段构建，优化镜像大小和安全性

# ===== 构建阶段 =====
FROM python:3.11-slim as builder

# 设置构建参数
ARG DEBIAN_FRONTEND=noninteractive
ARG PIP_NO_CACHE_DIR=1
ARG PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /build

# 复制项目文件
COPY pyproject.toml ./
COPY README.md ./
COPY src/ ./src/

# 创建虚拟环境并安装依赖
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 升级pip并安装项目
RUN pip install --upgrade pip setuptools wheel && \
    pip install -e .

# ===== 运行阶段 =====
FROM python:3.11-slim as runtime

# 设置运行时参数
ARG DEBIAN_FRONTEND=noninteractive
ARG APP_USER=mcpuser
ARG APP_UID=1000
ARG APP_GID=1000

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建应用用户
RUN groupadd -g ${APP_GID} ${APP_USER} && \
    useradd -u ${APP_UID} -g ${APP_GID} -m -s /bin/bash ${APP_USER}

# 复制虚拟环境
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 设置工作目录
WORKDIR /app

# 复制源代码（开发模式安装需要源代码）
COPY --from=builder /build/src ./src
COPY --from=builder /build/pyproject.toml ./
COPY --from=builder /build/README.md ./

# 创建必要的目录并设置权限
RUN mkdir -p /app/data /app/logs /app/temp && \
    chown -R ${APP_USER}:${APP_USER} /app

# 切换到应用用户
USER ${APP_USER}

# 设置环境变量
ENV PYTHONPATH=/app:/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    MCP_WEB_HOST=0.0.0.0 \
    MCP_WEB_PORT=8765 \
    MCP_DATA_DIR=/app/data \
    MCP_LOG_DIR=/app/logs \
    MCP_TEMP_DIR=/app/temp

# 暴露端口
EXPOSE 8765

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8765/health || exit 1

# 启动命令
CMD ["python", "-m", "mcp_feedback_enhanced", "server"]

# 备用启动命令（如果上面的命令不工作，可以取消注释下面的命令）
# CMD ["sh", "-c", "cd /app && python -c 'import sys; print(sys.path)' && python -m mcp_feedback_enhanced server"]
